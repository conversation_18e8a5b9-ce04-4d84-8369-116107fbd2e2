// Reusable status indicator components
import React from 'react';
import { CheckCircleIcon, XCircleIcon, AlertTriangleIcon } from '../icons';

export interface StatusBadgeProps {
  status: 'success' | 'warning' | 'error' | 'info' | 'pending';
  text: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  text, 
  size = 'md', 
  showIcon = true 
}) => {
  const getStatusClasses = () => {
    const baseClasses = 'inline-flex items-center gap-2 rounded-lg font-medium transition-colors duration-200';
    
    const sizeClasses = {
      sm: 'px-2 py-1 text-xs',
      md: 'px-3 py-1 text-sm',
      lg: 'px-4 py-2 text-base'
    };

    const statusClasses = {
      success: 'status-success border',
      warning: 'status-warning border',
      error: 'status-danger border',
      info: 'status-info border',
      pending: 'bg-gray-700 text-gray-300 border border-gray-600'
    };

    return `${baseClasses} ${sizeClasses[size]} ${statusClasses[status]}`;
  };

  const getIcon = () => {
    if (!showIcon) return null;

    const iconClasses = size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4';

    switch (status) {
      case 'success':
        return <CheckCircleIcon className={iconClasses} />;
      case 'error':
        return <XCircleIcon className={iconClasses} />;
      case 'warning':
      case 'pending':
        return <AlertTriangleIcon className={iconClasses} />;
      default:
        return null;
    }
  };

  return (
    <span className={getStatusClasses()}>
      {getIcon()}
      {text}
    </span>
  );
};

export interface ProgressBarProps {
  progress: number;
  max?: number;
  label?: string;
  showPercentage?: boolean;
  color?: 'primary' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  max = 100,
  label,
  showPercentage = true,
  color = 'primary',
  size = 'md'
}) => {
  const percentage = Math.min(100, Math.max(0, (progress / max) * 100));

  const getBarClasses = () => {
    const sizeClasses = {
      sm: 'h-1',
      md: 'h-2',
      lg: 'h-3'
    };

    return `w-full bg-gray-700 rounded-full ${sizeClasses[size]}`;
  };

  const getFillClasses = () => {
    const colorClasses = {
      primary: 'bg-brand-primary',
      success: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500'
    };

    return `h-full rounded-full transition-all duration-500 ease-in-out ${colorClasses[color]}`;
  };

  return (
    <div className="w-full">
      {label && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-300">{label}</span>
          {showPercentage && (
            <span className="text-sm text-gray-400">{Math.round(percentage)}%</span>
          )}
        </div>
      )}
      <div className={getBarClasses()}>
        <div 
          className={getFillClasses()}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

export interface ScoreDisplayProps {
  score: number;
  maxScore: number;
  label: string;
  threshold?: {
    good: number;
    warning: number;
  };
  showBar?: boolean;
}

export const ScoreDisplay: React.FC<ScoreDisplayProps> = ({
  score,
  maxScore,
  label,
  threshold = { good: 80, warning: 60 },
  showBar = true
}) => {
  const percentage = (score / maxScore) * 100;
  
  const getScoreColor = () => {
    if (percentage >= threshold.good) return 'success';
    if (percentage >= threshold.warning) return 'warning';
    return 'error';
  };

  const color = getScoreColor();

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-300">{label}</span>
        <div className="flex items-center gap-2">
          <span className="text-lg font-bold text-white">{score}</span>
          <span className="text-sm text-gray-400">/ {maxScore}</span>
        </div>
      </div>
      {showBar && (
        <ProgressBar 
          progress={score} 
          max={maxScore} 
          color={color}
          showPercentage={false}
          size="sm"
        />
      )}
    </div>
  );
};

export interface RiskIndicatorProps {
  risk: 'low' | 'medium' | 'high';
  label: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const RiskIndicator: React.FC<RiskIndicatorProps> = ({
  risk,
  label,
  description,
  size = 'md'
}) => {
  const getRiskClasses = () => {
    const baseClasses = 'flex items-center gap-3 p-3 rounded-lg border';
    
    const riskClasses = {
      low: 'bg-green-900/20 border-green-500/30 text-green-300',
      medium: 'bg-yellow-900/20 border-yellow-500/30 text-yellow-300',
      high: 'bg-red-900/20 border-red-500/30 text-red-300'
    };

    return `${baseClasses} ${riskClasses[risk]}`;
  };

  const getRiskDot = () => {
    const dotClasses = {
      sm: 'w-2 h-2',
      md: 'w-3 h-3',
      lg: 'w-4 h-4'
    };

    const colorClasses = {
      low: 'bg-green-500',
      medium: 'bg-yellow-500',
      high: 'bg-red-500'
    };

    return `${dotClasses[size]} ${colorClasses[risk]} rounded-full flex-shrink-0`;
  };

  return (
    <div className={getRiskClasses()}>
      <div className={getRiskDot()} />
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <span className="font-medium">{label}</span>
          <span className="text-xs uppercase font-bold">{risk}</span>
        </div>
        {description && (
          <p className="text-xs opacity-80 mt-1">{description}</p>
        )}
      </div>
    </div>
  );
};

export interface ComplianceIndicatorProps {
  compliant: boolean;
  score?: number;
  maxScore?: number;
  rule: string;
  description?: string;
}

export const ComplianceIndicator: React.FC<ComplianceIndicatorProps> = ({
  compliant,
  score,
  maxScore,
  rule,
  description
}) => {
  return (
    <div className={`p-4 rounded-lg border-l-4 ${
      compliant 
        ? 'bg-green-900/20 border-green-500 text-green-300' 
        : 'bg-red-900/20 border-red-500 text-red-300'
    }`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {compliant ? (
            <CheckCircleIcon className="w-4 h-4" />
          ) : (
            <XCircleIcon className="w-4 h-4" />
          )}
          <span className="font-medium">{rule}</span>
        </div>
        {score !== undefined && maxScore !== undefined && (
          <span className="text-sm">
            {score}/{maxScore}
          </span>
        )}
      </div>
      {description && (
        <p className="text-sm opacity-80">{description}</p>
      )}
    </div>
  );
};

export interface LoadingStateProps {
  stage: string;
  progress?: number;
  message?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  stage,
  progress,
  message
}) => {
  return (
    <div className="card p-8 text-center">
      <div className="flex flex-col items-center justify-center space-y-4">
        <div className="relative w-16 h-16">
          <div className="absolute inset-0 border-4 border-gray-700 rounded-full"></div>
          <div className="absolute inset-0 border-4 border-brand-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-white">{stage}</h3>
          {message && (
            <p className="text-sm text-gray-400">{message}</p>
          )}
        </div>

        {progress !== undefined && (
          <div className="w-full max-w-xs">
            <ProgressBar 
              progress={progress} 
              showPercentage={true}
              color="primary"
            />
          </div>
        )}

        <div className="flex gap-2">
          <div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-brand-secondary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>
    </div>
  );
};

export interface ErrorStateProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = "Error Occurred",
  message,
  onRetry,
  onDismiss
}) => {
  return (
    <div className="card p-8 border-l-4 border-red-500">
      <div className="flex items-start gap-4">
        <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center flex-shrink-0">
          <XCircleIcon className="w-4 h-4 text-white" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-red-300 mb-2">{title}</h3>
          <p className="text-sm text-gray-400 mb-4">{message}</p>
          <div className="flex gap-3">
            {onRetry && (
              <button 
                onClick={onRetry}
                className="btn-primary px-4 py-2 text-sm rounded transition-colors duration-200"
              >
                Try Again
              </button>
            )}
            {onDismiss && (
              <button 
                onClick={onDismiss}
                className="px-4 py-2 text-sm bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors duration-200"
              >
                Dismiss
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
