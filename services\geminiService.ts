import { GoogleGenAI } from "@google/genai";
import { Answer, Question, AnalysisResult } from '../types';
import { EVIDENCE_QUESTIONS } from '../constants';

if (!process.env.API_KEY) {
    console.warn("API_KEY environment variable not set. Using a placeholder. Report generation will fail.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY || "placeholder_api_key" });

interface EvidenceInfo {
    name: string;
    description: string;
    type: string;
}

const parseJsonResponse = <T>(text: string): T => {
    let jsonStr = text.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
        jsonStr = match[2].trim();
    }
    return JSON.parse(jsonStr) as T;
};

export const getAIKeyPoints = async (question: Question): Promise<{ keyPoints: string[] }> => {
    const prompt = `
        A user is answering a question for a legal evidence admissibility report. 
        The question is about the factor: "${question.factor}".
        The full question is: "${question.text}"

        Provide a few concise key points (3-4) that a strong answer should include.
        Focus on legal best practices and what a court would look for.

        Return your answer as a single, valid JSON object that conforms to the following TypeScript interface:
        \`\`\`typescript
        interface KeyPointsResult {
            keyPoints: string[]; // Array of concise strings.
        }
        \`\`\`
    `;
    try {
        const response = await ai.models.generateContent({
            model: "gemini-2.5-flash-preview-04-17",
            contents: prompt,
            config: {
                responseMimeType: "application/json",
                temperature: 0.3,
                thinkingConfig: { thinkingBudget: 0 }
            },
        });
        return parseJsonResponse<{ keyPoints: string[] }>(response.text);
    } catch (error) {
        console.error("Error getting AI key points:", error);
        throw new Error("Failed to get key points from AI. Please check API key and network.");
    }
};

export const getAICritique = async (question: Question, answer: string): Promise<{ strengths: string[], weaknesses: string[], recommendation: string }> => {
    const prompt = `
        You are a legal expert reviewing a draft response for an evidence admissibility questionnaire.

        The legal factor being addressed is: "${question.factor}"
        The specific question is: "${question.text}"
        The user's draft answer is: "${answer}"

        Provide a concise critique of the draft. Identify its main strengths, weaknesses, and provide a single, primary recommendation for improvement.
        
        Return your answer as a single, valid JSON object that conforms to the following TypeScript interface:
        \`\`\`typescript
        interface CritiqueResult {
          strengths: string[]; // 1-2 strengths. If none, return empty array.
          weaknesses: string[]; // 1-2 weaknesses. If none, return empty array.
          recommendation: string; // A single, actionable recommendation.
        }
        \`\`\`
    `;
    try {
        const response = await ai.models.generateContent({
            model: "gemini-2.5-flash-preview-04-17",
            contents: prompt,
            config: {
                responseMimeType: "application/json",
                temperature: 0.5,
                thinkingConfig: { thinkingBudget: 0 }
            },
        });
        return parseJsonResponse<{ strengths: string[], weaknesses: string[], recommendation: string }>(response.text);
    } catch (error) {
        console.error("Error getting AI critique:", error);
        throw new Error("Failed to get critique from AI. Please check API key and network.");
    }
};


export const generateComprehensiveAnalysis = async (answers: Answer[], evidenceInfo: EvidenceInfo): Promise<AnalysisResult> => {
  const answersText = answers
    .map(answer => {
      const question = EVIDENCE_QUESTIONS.find(q => q.id === answer.questionId);
      return `Section: ${question?.section}\nFactor: ${question?.factor}\nQuestion: ${question?.text}\nUser's Answer: ${answer.value}\n\n`;
    })
    .join('');

  const prompt = `
    You are an expert legal analyst specializing in the admissibility of digital evidence in U.S. courts.

    First, here is the description of the digital evidence being analyzed. This context is crucial for your analysis. Refer to it when assessing the user's answers.
    - Evidence Name/ID: ${evidenceInfo.name || 'Not provided'}
    - Evidence Type: ${evidenceInfo.type || 'Not provided'}
    - Description: ${evidenceInfo.description || 'Not provided'}

    Now, analyze the following user-provided answers for each factor of evidence admissibility. Your response MUST be a single, valid JSON object that conforms to the TypeScript interface provided below.

    **Analysis Steps:**
    1.  **Factor-by-Factor Analysis:** For each factor, provide:
        - A summary of the user's response.
        - An admissibility confidence level: 'High', 'Medium', or 'Low'.
        - A list of strengths.
        - A list of weaknesses.
        - Actionable recommendations to mitigate weaknesses.
        - Potential cross-examination questions an opposing attorney might ask.
        - Recommended text for a motion to suppress.
    2.  **Section Summaries:** For each section (e.g., 'Foundational Admissibility'), write a high-level summary.
    3.  **Overall Conclusion:** Write a final, high-level conclusion for the entire body of evidence.
    4.  **Executive Summary:** After completing all analysis, create an executive summary.
        - For 'overallConfidence', choose the level that best represents the entire analysis.
        - For 'confidenceBreakdown', count the number of factors at each confidence level.
        - For 'topRecommendations', synthesize and extract the three most critical and impactful actionable recommendations from the entire report.

    **JSON Output Structure:**
    Your entire output must be a single JSON object matching this TypeScript interface:
    \`\`\`typescript
    interface ExecutiveSummary {
      overallConfidence: 'High' | 'Medium' | 'Low';
      confidenceBreakdown: {
        high: number;
        medium: number;
        low: number;
      };
      topRecommendations: string[]; // The 3 most critical recommendations.
    }

    interface FactorAnalysis {
      factor: string;
      summary: string;
      admissibilityConfidence: 'High' | 'Medium' | 'Low';
      strengths: string[];
      weaknesses: string[];
      actionableRecommendations: string[];
      crossExaminationQuestions: string[];
      recommendedSuppressionText: string[];
    }

    interface AnalysisSection {
      sectionTitle: string;
      sectionSummary: string;
      factorAnalyses: FactorAnalysis[];
    }

    interface AnalysisResult {
      executiveSummary: ExecutiveSummary; // **NEW** Populate this summary.
      overallConclusion: string;
      analysisSections: AnalysisSection[];
    }
    \`\`\`

    Here are the user's answers:
    ---
    ${answersText}
    ---

    Now, generate the complete JSON output. Ensure every field is populated. For recommendations, be specific, practical, and legally sound.
    `;

    try {
        const response = await ai.models.generateContent({
            model: "gemini-2.5-flash-preview-04-17",
            contents: prompt,
            config: {
                responseMimeType: "application/json",
                temperature: 0.4,
            },
        });

        return parseJsonResponse<AnalysisResult>(response.text);

    } catch (error) {
        console.error("Error generating comprehensive analysis:", error);
        throw new Error("Failed to generate the analysis. Please check the API key and network connection.");
    }
};
